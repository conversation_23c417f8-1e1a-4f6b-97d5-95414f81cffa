import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { GetPropertiessQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';
import { Pagination } from '@app/common';
import { Prisma } from '@prisma/client';
import { PropertyTypeGrpcService } from '../../../property-type/service/property-type.grpc.service';
import { Metadata } from '@grpc/grpc-js';
import { PackageService } from '../../../internal-client/services';
import { IntegrationDlpService } from '../../../integration/services';
import { lastValueFrom } from 'rxjs';

@QueryHandler(GetPropertiessQuery)
export class GetPropertiesHandler
  implements IQueryHandler<GetPropertiessQuery>
{
  constructor(
    private prisma: PrismaService,
    private propertyTypeGrpc: PropertyTypeGrpcService,
    private integrationService: IntegrationDlpService,
    private pkgGrpc: PackageService,
  ) {}

  async execute(query: GetPropertiessQuery) {
    const { user, args } = query;
    const search = args.search || '';
    const page = Number(args?.page || 1);
    const limit = Number(args?.limit || 50);
    const sortType = args?.sortType ? args.sortType : 'desc';

    const baseWhere = {
      userId: user.id,
    };

    let whereClause;

    if (search && search.trim() !== '') {
      whereClause = {
        ...baseWhere,
        property: {
          OR: [
            { companyName: { contains: search, mode: 'insensitive' } },
            { brandName: { contains: search, mode: 'insensitive' } },
            { cid: { contains: search, mode: 'insensitive' } },
          ],
        },
      };
    } else {
      whereClause = baseWhere;
    }

    const items = await Pagination<any, Prisma.UserPropertyFindManyArgs>(
      this.prisma.userProperty,
      {
        where: whereClause,
        orderBy: { createdAt: sortType },
        include: {
          property: true,
          user: true,
        },
      },
      {
        limit: limit,
        page: page,
      },
    );

    let propertyTypes: { id: string; name: string }[] = [];

    try {
      propertyTypes = await Promise.all(
        items.data.map(async (item) => {
          const id = item.property.propertyTypeId;
          if (!id) return { id: '', name: '' };

          try {
            const propertyType = await lastValueFrom(
              this.propertyTypeGrpc.getOnePropertyType({ id }),
            );
            return { id: propertyType.id, name: propertyType.name };
          } catch (error) {
            console.error(`Error fetching property type with id ${id}:`, error);
            return { id: '', name: '' };
          }
        }),
      );
    } catch (error) {
      console.error('Error in property types Promise.all:', error);
      propertyTypes = items.data.map(() => ({ id: '', name: '' }));
    }

    let packages: any[] = [];

    try {
      packages = await Promise.all(
        items.data.map(async (itm) => {
          const payload: any = {
            propertyId: itm.property.id,
          };

          const meta = new Metadata();
          const pkg = await lastValueFrom(
            this.pkgGrpc.client.listPackage(
              {
                query: JSON.stringify(payload),
              },
              meta,
            ),
          );

          return pkg.data;
        }),
      );
    } catch (error) {
      console.error('Error fetching packages:', error);
    }

    const result = await Promise.all(
      items.data.map(async (itm, index) => {
        const { propertyTypeId, requestId, ...propertyData } = itm.property;

        let licenseStatusStatus = null;
        let mustVerif = false;
        let propertyForm = false;

        const otherUsers = await this.prisma.userProperty.findMany({
          where: {
            propertyId: itm.property.id,
            userId: { not: user.id },
          },
        });

        if (requestId) {
          try {
            const licenseStatus =
              await this.integrationService.checkLicenseStatus(requestId);
            licenseStatusStatus = licenseStatus?.status || null;
          } catch (error) {
            console.error('Error fetching license status:', error);
          }
        }

        if (licenseStatusStatus === 'pending') {
          mustVerif = true;
        }

        if (itm.property.status === 'draft') {
          propertyForm = true;
        }

        const userOperator = otherUsers.length > 0;
        // const playerNode = packages[index]
        //   .filter((dt) => dt.itemType === 'PLAN')[0]
        //   ?.packageFeature.filter((ft) => ft.id === 'f-002')[0];

        const packageList = packages[index] || [];
        const planPackage = packageList.find((dt) => dt.itemType === 'PLAN');
        const playerNode = planPackage?.packageFeature?.find(
          (ft) => ft.id === 'f-002',
        );

        return {
          ...propertyData,
          licenseStatus: licenseStatusStatus,
          totalActivation: playerNode ? Number(playerNode.qty) : 0,
          totalUsedActivation: playerNode ? Number(playerNode.quota) : 0,
          propertyType: propertyTypes[index] || { id: '', name: '' },
          package: packages[index] || [],
          userOperator: userOperator || '',
          requestId: requestId || '',
          mustVerifyTemporaryLicense: mustVerif,
          mustCompletePropertyForm: propertyForm,
        };
      }),
    );

    return {
      ...items,
      data: result,
    };
  }
}
