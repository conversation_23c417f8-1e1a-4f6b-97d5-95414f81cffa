import { BadRequestException } from '@nestjs/common';
import { <PERSON><PERSON><PERSON>y<PERSON><PERSON><PERSON>, QueryHandler } from '@nestjs/cqrs';
import { GetUsersQuery } from '../impl';
import { UserGrpcService } from 'apps/api-membership/src/internal-client/user.service';
import { lastValueFrom } from 'rxjs';
import { status } from '@grpc/grpc-js';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetUsersQuery)
export class GetUsersHandler implements IQueryHandler<GetUsersQuery> {
  constructor(
    private readonly userService: UserGrpcService,
    private prisma: PrismaService,
  ) {}

  async execute(query: GetUsersQuery) {
    const { args, user } = query;
    let propertyIds: string[] = [];

    if (args.propertyId) {
      propertyIds = [args.propertyId];
    } else {
      const userProperties = await this.prisma.userProperty.findMany({
        where: {
          userId: user.id,
        },
        select: {
          propertyId: true,
        },
      });
      propertyIds = userProperties.map((p) => p.propertyId);
    }

    try {
      const { users = [] } =
        (await lastValueFrom(
          this.userService.getUsersByPropertyId({
            propertyId: propertyIds,
          }),
        )) || {};

      return {
        data: users,
      };
    } catch (error) {
      // Handle NOT_FOUND case by returning empty array
      console.error(error.message);
      if (error?.code === status.NOT_FOUND) {
        return {
          data: [],
        };
      }
      throw new BadRequestException('Failed to fetch users');
    }
  }
}
