import { Pagination } from '@app/common';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from '@nestjs/cqrs';
import { Prisma } from '@prisma/client';
import { GetOrdersQuery } from '../impl';
import { PrismaService } from 'nestjs-prisma';

@QueryHandler(GetOrdersQuery)
export class GetOrdersHandler implements IQueryHandler<GetOrdersQuery> {
  constructor(private prisma: PrismaService) {}

  async execute(query: GetOrdersQuery) {
    const { user, args } = query;

    const items = await Pagination<any, Prisma.OrderFindManyArgs>(
      this.prisma.order,
      {
        orderBy: { createdAt: 'desc' },
        where: {
          AND: [
            {
              property: {
                users: {
                  some: {
                    user: {
                      id: user.id,
                    },
                  },
                },
              },
            },
            { status: args?.status },
          ],
        },
        include: {
          details: { include: { orderPackageDetail: true } },
          property: true,
        },
      },
      { page: args.page, limit: args.limit },
    );

    const orderIds = items.data.map((item) => item.id);

    const payments = await this.prisma.orderPayment.findMany({
      where: { orderId: { in: orderIds } },
    });

    const paymentsByOrderId = payments.reduce(
      (map, payment) => {
        map[payment.orderId] = payment;
        return map;
      },
      {} as { [key: string]: { url: string | null } },
    );

    const data = items.data.map((item) => {
      const status = item.tag === 'trial' ? 'Trial' : item.status;

      return {
        id: item.id,
        price: item.basePrice,
        duration: item.duration,
        status: status,
        invoice: item.invoice,
        name: item.name,
        description: item.description,
        orderAt: item.createdAt,
        checkoutUrl: paymentsByOrderId[item.id]?.url || null,
        property: item.property,
      };
    });
    return Object.assign(items, { data: data });
  }
}
