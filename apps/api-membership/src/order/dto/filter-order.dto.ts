import { BaseFilterDto } from '@app/common';
import { ApiProperty, ApiPropertyOptional, OmitType } from '@nestjs/swagger';
import { $Enums } from '@prisma/client';
import { IsEnum, IsOptional, IsString } from 'class-validator';

export class FilterOrderDto extends OmitType(BaseFilterDto, [
  'sort',
  'sortType',
] as const) {
  @IsEnum($Enums.OrderStatus)
  @ApiProperty({ enum: $Enums.OrderStatus })
  @IsOptional()
  @ApiPropertyOptional()
  status: $Enums.OrderStatus;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  tag?: string;

  @IsString()
  @IsOptional()
  @ApiPropertyOptional()
  propertyId?: string;
}
