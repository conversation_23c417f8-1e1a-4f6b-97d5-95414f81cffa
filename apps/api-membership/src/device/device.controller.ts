import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  Get,
  InternalServerErrorException,
  Ip,
  NotFoundException,
  Param,
  Patch,
  Post,
  Query,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from '../auth/decorator/user.decorator';
import { JwtGuard } from '../auth/guards/jwt.guard';
import { ICurrentUser } from '../auth/strategies/types/user.type';
import { FilterDeviceDto } from './dto/filter-device.dto';
import { DeviceGrpcService } from '../internal-client/device.service';
import { PrismaService } from 'nestjs-prisma';
import { status } from '@grpc/grpc-js';
import { LoggedSessionCreate } from '../auth/types';
import { QueryBus } from '@nestjs/cqrs';
import { SsoLoginByDeviceIdQuery } from './queries';
@ApiTags('Device')
@Controller('device')
@ApiBearerAuth()
@UseGuards(JwtGuard)
export class DeviceController {
  constructor(
    private deviceService: DeviceGrpcService,
    private readonly prismaService: PrismaService,
    private queryBus: QueryBus,
  ) {}

  @Get()
  async findAll(@User() user: ICurrentUser, @Query() filter: FilterDeviceDto) {
    const userProperties = await this.prismaService.userProperty.findMany({
      where: { userId: user.id },
      select: { propertyId: true },
    });

    if (userProperties.length === 0) {
      return {
        data: [],
        meta: {
          total: 0,
          lastPage: 0,
          currentPage: 1,
          limit: filter.limit,
          prev: null,
          next: null,
        },
      };
    }

    const propertyIds = userProperties.map((p) => p.propertyId);
    const filterProperties = filter?.propertyIds
      ? filter.propertyIds.split(',').map(String)
      : [];

    const properties =
      filterProperties.length > 0
        ? propertyIds.filter((item) => filterProperties.includes(item))
        : propertyIds;

    const query = {
      limit: filter?.limit,
      page: filter?.page,
      search: filter?.search,
      sort: filter?.sort,
      sortType: filter?.sortType,
      propertyIds: properties,
    };

    const resp = await this.deviceService
      .getDevicesByPropertyIn({ query: JSON.stringify(query) })
      .toPromise();

    return {
      data: resp.data,
      meta: resp.meta,
    };
  }

  // @Get('/total-device')
  // async totalDevice(
  //   @User() user: ICurrentUser,
  //   @Query() filter: TotalDeviceDto,
  // ) {
  //   try {
  //     const properties = await this.prismaService.userProperty.findMany({
  //       where: { userId: user.id },
  //       select: { propertyId: true },
  //     });
  //     if (!properties) {
  //       return { total: 0 };
  //     }

  //     const propertyIds = properties.map((p) => p.propertyId);
  //     const request: GetTotalDeviceRequest = {
  //       Ids: propertyIds,
  //       isActive: filter.isActive,
  //       isOnline: filter.isOnline,
  //     };

  //     return await this.deviceService.getTotalDevice(request).toPromise();
  //   } catch (err) {
  //     console.log(err);
  //     if (err.code == status.NOT_FOUND) {
  //       throw new NotFoundException('device not found');
  //     } else {
  //       throw new InternalServerErrorException('Internal Server Error');
  //     }
  //   }
  // }

  // Option 1: Using query parameter
  @Get('activations')
  async getActivations(@Query('propertyId') propertyId: string) {
    if (!propertyId) {
      throw new BadRequestException('Property ID is required');
    }

    try {
      const { data = [] } = await this.deviceService
        .getActivationsByProperty({
          propertyId,
        })
        .toPromise();

      return data;
    } catch (err) {
      if (err.code === status.NOT_FOUND) {
        throw new NotFoundException('Activations not found');
      }
      throw new InternalServerErrorException('Failed to fetch activations');
    }
  }

  @Get(':id')
  async findOne(@User() user: ICurrentUser, @Param('id') id: string) {
    try {
      return await this.deviceService
        .getDevice({
          query: JSON.stringify({ propertyId: user.propertyId, id: id }),
        })
        .toPromise();
    } catch (err) {
      if (err.code == status.NOT_FOUND) {
        throw new NotFoundException('device not found');
      } else {
        throw new InternalServerErrorException('Internal Server Error');
      }
    }
  }

  @Get(':id/sso')
  @ApiBearerAuth()
  @UseGuards(JwtGuard)
  loginSSO(
    @User() user: ICurrentUser,
    @Param('id') deviceId: string,
    @Ip() ip: string,
    @Req() req: Request,
  ) {
    const loggedSession: LoggedSessionCreate = {
      ip: ip,
      media: req.headers['user-agent'],
    };
    return this.queryBus.execute(
      new SsoLoginByDeviceIdQuery(user, deviceId, loggedSession),
    );
  }
}
